package cn.genn.trans.upm.domain.upm.service;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.genn.core.exception.BusinessException;
import cn.genn.core.exception.CheckException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.dto.UpmAuthChangeEventDimensionEnum;
import cn.genn.trans.upm.application.service.action.SpringEventPublishService;
import cn.genn.trans.upm.domain.upm.model.entity.OfficialAccountUser;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmRole;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.domain.upm.model.valobj.Password;
import cn.genn.trans.upm.domain.upm.repository.AccountRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.domain.upm.repository.UserRoleRepository;
import cn.genn.trans.upm.infrastructure.converter.UpmUserConverter;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.*;
import cn.genn.trans.upm.infrastructure.repository.po.*;
import cn.genn.trans.upm.infrastructure.utils.RSAUtil;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.*;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.CollectionUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class UserDomainService {

    @Resource
    private UserRepository repository;
    @Resource
    private UserRoleRepository userRoleRepository;
    @Resource
    private UpmAccountMapper accountMapper;
    @Resource
    private AccountRepository accountRepository;
    @Resource
    private UpmUserConverter upmUserConverter;
    @Resource
    private SpringEventPublishService eventPublishService;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private SsoAuthProperties ssoAuthProperties;
    @Resource
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private UpmRoleMapper upmRoleMapper;
    @Resource
    private UpmUserThirdMapper userThirdMapper;
    @Resource
    private UpmUserRoleMapper userRoleMapper;
    @Resource
    private UpmRoleMapper roleMapper;
    @Autowired
    private UpmAccountMapper upmAccountMapper;
    @Autowired
    private RoleDomainService roleDomainService;
    @Resource
    private UserRepository userRepository;

    /**
     * 创建租户类型用户
     */
    public UpmUserPO createTenantUser(UpmUser upmUser) {
        upmUser.setType(UserTypeEnum.TENANT);

        // 添加账号
        UpmAccount account = upmUser.getAccount();
        Password password = Password.create(account.getPassword());
        UpmAccountPO upmAccountPO = new UpmAccountPO()
            .setUsername(account.getUsername())
            .setPassword(password.getPassword())
            .setSystemId(upmUser.getSystemId())
            .setTelephone(upmUser.getTelephone())
            .setCompanyName(upmUser.getCompanyName())
            .setSaleName(upmUser.getSaleName())
            .setSalt(password.getSalt());
        accountMapper.insert(upmAccountPO);
        // 添加用户
        UpmUserPO upmUserPO = upmUserConverter.entity2PO(upmUser)
            .setAccountId(upmAccountPO.getId());
        upmUserMapper.insert(upmUserPO);

        // 关联角色
        upmUser.setId(upmUserPO.getId());
        if (CollectionUtil.isNotEmpty(upmUser.getRoleIdList())) {
            userRoleRepository.relatedRole(upmUser, true);
        }
        return upmUserPO;
    }


    // 同一系统下账号名称唯一
    public void checkAccountNameisUniqueInSystem(String userName, Long systemId) {
        UpmAccountPO upmAccountPO = accountMapper.selectByUsernameAndSystemId(userName, systemId);
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_EXIST);
        }
    }

    // 同一系统下手机号唯一
    public void checkPhoneIsUniqueInSystem(String phone, Long systemId) {
        if (StringUtils.isBlank(phone)) {
            return;
        }
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(phone, systemId);
        if (ObjUtil.isNotNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.PHONE_EXIST);
        }
    }

    public void checkAccountBeforeBatchInsertBySystemType(List<UpmAccountPO> accountPOS) {
        if (CollectionUtils.isEmpty(accountPOS)) {
            return;
        }
        // 多系统新增员工，员工名称、手机号一致，只此一个入口，单一校验即可
        UpmAccountPO upmAccountPO = accountPOS.get(0);
        checkAccountNameisUniqueInSystem(upmAccountPO.getUsername(), upmAccountPO.getSystemId());
        checkPhoneIsUniqueInSystem(upmAccountPO.getTelephone(), upmAccountPO.getSystemId());
    }

    public List<UpmUser> batchCreateTenantUserBySystem(List<UpmUser> userList) {
        if (CollectionUtils.isEmpty(userList)) {
            return Collections.emptyList();
        }

        // 账户创建-创建不同系统的账户，账户用户一对一，（适用于站端）
        List<UpmAccountPO> accountPOList = userList.stream().map(upmUser -> {
            UpmAccount account = upmUser.getAccount();
            Password password = Password.create(account.getPassword());
            UpmAccountPO upmAccountPO = new UpmAccountPO()
                .setUsername(account.getUsername())
                .setPassword(password.getPassword())
                .setSystemId(upmUser.getSystemId())
                .setTelephone(upmUser.getTelephone())
                .setSalt(password.getSalt());
            return upmAccountPO;
        }).collect(Collectors.toList());
        checkAccountBeforeBatchInsertBySystemType(accountPOList);
        accountRepository.saveBatch(accountPOList);

        Map<Long, Long> systemAccountIdMap = accountPOList.stream()
            .collect(Collectors.toMap(k -> k.getSystemId(), v -> v.getId(), (k1, k2) -> k1));

        // 用户创建--关联创建的账户
        List<UpmUserPO> userPos = userList.stream().map(upmUser -> {
            upmUser.setType(UserTypeEnum.TENANT);
            UpmUserPO upmUserPO = upmUserConverter.entity2PO(upmUser)
                .setAccountId(systemAccountIdMap.get(upmUser.getSystemId()));
            return upmUserPO;
        }).collect(Collectors.toList());
        repository.saveBatchPOList(userPos);

        List<UpmUser> upmUsers = upmUserConverter.PO2Entity(userPos);

        // 关联角色
        if (CollectionUtils.isNotEmpty(userList.get(0).getRoleIdList())) {

            List<Long> mainRoleIds = userList.get(0).getRoleIdList();
            List<String> authKeyList = userList.stream().map(UpmUser::getAuthKey).collect(Collectors.toList());
            List<UpmRole> upmRoles = roleDomainService.selectByAuthKeyListAndMainRoleIds(authKeyList, mainRoleIds);
            Map<Long, List<Long>> systemRoleIdsMap = Optional.ofNullable(upmRoles).orElse(new ArrayList<>(0))
                .stream().collect(Collectors.groupingBy(UpmRole::getSystemId, Collectors.mapping(UpmRole::getId, Collectors.toList())));
            upmUsers.forEach(upmUser -> {
                    upmUser.setRoleIdList(systemRoleIdsMap.get(upmUser.getSystemId()));
                }
            );
            userRoleRepository.batchRelatedRoleBySystemType(upmUsers, true);
        }
        return upmUsers;
    }


    public List<UpmUser> selectBySystemsAndMainUserId(List<Long> systemIds, Long userId) {
        UpmUserPO upmUserPO = upmUserMapper.selectById(userId);
        if (Objects.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        UpmAccountPO upmAccountPO = upmAccountMapper.selectById(upmUserPO.getAccountId());
        if (Objects.isNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.ACCOUNT_NOT_EXIST_CANT_UPDATE_USER);
        }
        List<UpmAccountPO> accountPOList = upmAccountMapper.selectByUsernameAndSystemIds(upmAccountPO.getUsername(), systemIds);
        List<Long> accountIds = accountPOList.stream().map(UpmAccountPO::getId).collect(Collectors.toList());
        List<UpmUserPO> upmUserPOS = upmUserMapper.selectByAccountIds(accountIds);
        return Optional.ofNullable(upmUserPOS).map(upmUserConverter::PO2Entity).orElse(Collections.EMPTY_LIST);
    }

    /**
     * 创建游客
     */
    public Boolean createRegisterUser(UpmUser upmUser) {
        upmUser.setType(UserTypeEnum.SYSTEM);
        upmUser.setTenantId(ssoAuthProperties.getPlatformTenantId());

        // 添加账号
        UpmAccount account = upmUser.getAccount();
        String pd = null;
        try {
            pd = RSAUtil.decryptBase64(account.getPassword());
        } catch (Exception e) {
            log.error("password 解密失败", e);
            throw new CheckException(MessageCode.USER_NOT_EXIST);
        }
        Password password = Password.create(pd);
        UpmAccountPO upmAccountPO = new UpmAccountPO()
            .setTelephone(account.getTelephone())
            .setUsername(account.getUsername())
            .setPassword(password.getPassword())
            .setSystemId(account.getSystemId())
            .setSalt(password.getSalt());
        accountMapper.insert(upmAccountPO);
        // 添加用户
        String authKey = AuthKeyUtil.getPlatformKey(upmUser.getSystemId(), ssoAuthProperties.getPlatformTenantId());
        UpmUserPO upmUserPO = upmUserConverter.entity2PO(upmUser)
            .setAccountId(upmAccountPO.getId())
            .setTelephone(account.getTelephone())
            .setAuthKey(authKey);
        upmUserMapper.insert(upmUserPO);
        upmUser.setId(upmUserPO.getId());

        // 关联角色
        UpmRolePO rolePO = upmRoleMapper.queryByCodeAndAuthKey(AuthKeyUtil.getPlatformKey(upmUser.getSystemId(), ssoAuthProperties.getPlatformTenantId()), upmSeverProperties.getDefaultTmsTourist());
        upmUser.setRoleIdList(Collections.singletonList(rolePO.getId()));
        userRoleRepository.relatedRole(upmUser, true);
        return true;
    }

    /**
     * 修改租户用户
     */
    public Boolean update(UpmUser upmUser, boolean clearSign) {
        // 修改账号
        LambdaUpdateWrapper<UpmAccountPO> wrapper = Wrappers.lambdaUpdate(UpmAccountPO.class)
            .eq(UpmAccountPO::getId, upmUser.getAccountId())
            .set(ObjUtil.isNotNull(upmUser.getCompanyName()), UpmAccountPO::getCompanyName, upmUser.getCompanyName())
            .set(ObjUtil.isNotNull(upmUser.getSaleName()),UpmAccountPO::getSaleName, upmUser.getSaleName())
            .set(ObjUtil.isNotNull(upmUser.getSaleName()),UpmAccountPO::getTelephone, upmUser.getTelephone());
        accountMapper.update(wrapper);
        // 修改用户信息
        repository.update(upmUserConverter.entity2PO(upmUser),upmUser.getEffectiveTimeEnable());
        // 关联角色
        userRoleRepository.relatedRole(upmUser, clearSign);
        eventPublishService.publishUpmAuthChangeEvent(UpmAuthChangeEventDimensionEnum.USER, upmUser.getId());
        return true;
    }


    public Boolean updateBySystemType(UpmUser upmUser, boolean clearSign) {
        // 数据校验
        UpmUserPO upmUserPO = repository.selectById(upmUser.getId());
        if (Objects.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST_ERROR);
        }
        // 手机号变更校验
        if (StrUtil.isNotBlank(upmUser.getTelephone()) && !upmUser.getTelephone().equals(upmUserPO.getTelephone())) {
            this.checkPhoneIsUniqueInSystem(upmUser.getTelephone(), upmUser.getSystemId());
        }
        return update(upmUser, clearSign);
    }

    public boolean resetPassword(UpmUser upmUser) {
        Password password = Password.create(upmUser.getAccount().getPassword());
        LambdaUpdateWrapper<UpmAccountPO> accountWrapper = Wrappers.lambdaUpdate(UpmAccountPO.class)
            .eq(UpmAccountPO::getId, upmUser.getAccountId())
            .set(UpmAccountPO::getPassword, password.getPassword())
            .set(UpmAccountPO::getSalt, password.getSalt())
            .set(UpmAccountPO::getPasswordStatus, PasswrodStatusEnum.DEFAULT)
            .set(UpmAccountPO::getPdUpdateTime, LocalDateTime.now());
        accountMapper.update(accountWrapper);
        return true;
    }

    /**
     * 批量删除
     */
    public Boolean removeUsers(List<UpmUser> upmUsers) {
        // 删除用户
        List<Long> userIdList = upmUsers.stream().map(UpmUser::getId).collect(Collectors.toList());
        repository.batchDelete(userIdList);
        // 删除账号
        List<Long> accountIdList = upmUsers.stream().map(UpmUser::getAccountId).collect(Collectors.toList());
        accountMapper.deleteBatchIds(accountIdList);
        return true;
    }

    /**
     * 关联角色
     */
    public Boolean relatedRole(UpmUser upmUser) {
        return userRoleRepository.relatedRole(upmUser, true);
    }

    /**
     * 更新个人信息
     */
    public Boolean changeUserInfo(UpmUser upmUser) {
        return repository.changeUserInfo(upmUser);
    }

    /**
     * 绑定手机
     */
    public Boolean bindPhone(UpmUser upmUser) {
        return repository.bindPhone(upmUser);
    }

    /**
     * 修改密码
     *
     * @return
     */
    public Boolean changePassword(UpmAccount account) {
        String pd = null;
        try {
            pd = RSAUtil.decryptBase64(account.getPassword());
        } catch (Exception e) {
            log.error("password 解密失败", e);
            throw new CheckException(MessageCode.USER_NOT_EXIST);
        }
        Password password = Password.create(pd);
        account.setPassword(password.getPassword());
        account.setSalt(password.getSalt());
        return accountRepository.updatePassword(account);
    }

    /**
     * 创建超级管理员
     */
    public List<UpmUserPO> createSuperUser(List<Long> systemIdList, List<UpmRolePO> roleList, String accountName, String telephone, String nick) {
        return createDefaultSuperUser(systemIdList, roleList, accountName, upmSeverProperties.getDefaultPassword(), telephone, nick);
    }

    /**
     * 创建子系统超级管理员
     *
     * @param systemIdList
     * @param roleList
     * @return
     */
    public List<UpmUserPO> createDefaultSuperUser(List<Long> systemIdList, List<UpmRolePO> roleList, String accountName, String defaultPassword, String telephone, String nick) {
        // 创建账号
        List<UpmAccountPO> insertAccountList = new ArrayList<>();
        for (Long systemId : systemIdList) {
            UpmAccountPO upmAccountPO = new UpmAccountPO();
            upmAccountPO.setUsername(accountName);
            Password password = Password.create(StringUtils.isNotBlank(defaultPassword) ? defaultPassword : upmSeverProperties.getDefaultPassword());
            upmAccountPO.setPassword(password.getPassword());
            upmAccountPO.setSystemId(systemId);
            upmAccountPO.setTelephone(telephone);
            upmAccountPO.setSalt(password.getSalt());
            upmAccountPO.setType(AccountTypeEnum.SYSTEM);
            insertAccountList.add(upmAccountPO);
        }
        List<UpmAccountPO> accountPOS = accountRepository.saveBatch(insertAccountList);
        Map<Long, Long> systemAccountMap = accountPOS.stream().collect(Collectors.toMap(UpmAccountPO::getSystemId, UpmAccountPO::getId));
        // 创建用户
        List<UpmUserPO> upmUserPOList = new ArrayList<>();
        for (UpmRolePO rolePO : roleList) {
            UpmUserPO upmUserPO = new UpmUserPO()
                .setAccountId(systemAccountMap.get(rolePO.getSystemId()))
                .setTenantId(rolePO.getTenantId())
                .setSystemId(rolePO.getSystemId())
                .setAuthKey(rolePO.getAuthKey())
                .setTelephone(telephone)
                .setType(UserTypeEnum.SYSTEM)
                .setNick(StrUtil.isNotBlank(nick) ? nick : upmSeverProperties.getDefaultSuperNick());
            upmUserPOList.add(upmUserPO);
        }
        repository.saveBatchList(upmUserPOList);

        // 用户角色关联关系
        List<UpmUserRolePO> userUserRolePOList = new ArrayList<>();
        Map<String, Long> TenantSystemMap = roleList.stream()
            .collect(Collectors.toMap(upmRolePO -> upmRolePO.getTenantId() + "-" + upmRolePO.getSystemId(), UpmRolePO::getId));
        for (UpmUserPO upmUserPO : upmUserPOList) {
            UpmUserRolePO upmUserRolePO = new UpmUserRolePO();
            upmUserRolePO.setUserId(upmUserPO.getId());
            Long roleId = TenantSystemMap.get(upmUserPO.getTenantId() + "-" + upmUserPO.getSystemId());
            upmUserRolePO.setRoleId(roleId);
            userUserRolePOList.add(upmUserRolePO);
        }
        userRoleRepository.saveBatch(userUserRolePOList);
        return upmUserPOList;
    }


    public boolean deleteByTenantId(Long tenantId) {
        // 删除账号
        List<UpmUserPO> upmUserPOList = repository.selectByTenantId(tenantId);
        if (CollUtil.isEmpty(upmUserPOList)) {
            return true;
        }
        List<Long> accountIdList = upmUserPOList.stream().map(UpmUserPO::getAccountId).distinct().collect(Collectors.toList());
        accountMapper.deleteBatchIds(accountIdList);
        // 删除用户
        List<Long> userIdList = upmUserPOList.stream().map(UpmUserPO::getId).collect(Collectors.toList());
        repository.batchDelete(userIdList);
        return true;
    }

    /**
     * 创建小程序用户
     */
    public UserWxInfoDTO createMiniUser(Long systemId, String appId, String telephone, WxMaJscode2SessionResult wxMaUserInfo) {
        Long tenantId = ssoAuthProperties.getPlatformTenantId();
        String authKey = AuthKeyUtil.getPlatformKey(systemId, ssoAuthProperties.getPlatformTenantId());
        // 手机号查账号
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(telephone, systemId);
        if (ObjUtil.isNull(upmAccountPO)) {
            // 不存在,新建
            UpmAccountPO insertAccountPO = this.insertWxAccount(telephone, systemId);
            UpmUserPO insertUserPO = this.insertWxUser(insertAccountPO.getId(), tenantId, systemId, authKey, telephone, null);
            UpmUserThirdPO upmUserThirdPO = this.insertUserThird(insertUserPO.getId(), appId, wxMaUserInfo);
            return this.arrangeWxInfo(insertAccountPO, insertUserPO, upmUserThirdPO);
        }
        // 司机userId查用户
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndAuthKey(upmAccountPO.getId(), authKey);
        if (ObjUtil.isNull(upmUserPO)) {
            // 不存在,新建
            UpmUserPO insertUserPO = this.insertWxUser(upmAccountPO.getId(), tenantId, systemId, authKey, telephone, null);
            UpmUserThirdPO upmUserThirdPO = this.insertUserThird(insertUserPO.getId(), appId, wxMaUserInfo);
            return this.arrangeWxInfo(upmAccountPO, insertUserPO, upmUserThirdPO);
        }
        // 新建三方关联
        UpmUserThirdPO upmUserThirdPO = this.insertUserThird(upmUserPO.getId(), appId, wxMaUserInfo);
        return this.arrangeWxInfo(upmAccountPO, upmUserPO, upmUserThirdPO);
    }

    /**
     * 账号登录关联三方表
     */
    public UserWxInfoDTO accountCreateMiniUser(Long systemId, String appId, String username, WxMaJscode2SessionResult wxMaUserInfo) {
        String authKey = AuthKeyUtil.getPlatformKey(systemId, ssoAuthProperties.getPlatformTenantId());
        // 查账号
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(username, systemId);
        if (ObjUtil.isNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 司机userId查用户
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndAuthKey(upmAccountPO.getId(), authKey);
        if (ObjUtil.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 新建三方关联
        UpmUserThirdPO upmUserThirdPO = this.insertUserThird(upmUserPO.getId(), appId, wxMaUserInfo);
        return this.arrangeWxInfo(upmAccountPO, upmUserPO, upmUserThirdPO);
    }

    /**
     * 创建司机账号
     *
     * @param telephone
     * @param nick
     */
    @Transactional(rollbackFor = Exception.class)
    public UserWxInfoDTO createRegisterDriver(String telephone, String nick,Long systemId) {
        Long tenantId = ssoAuthProperties.getPlatformTenantId();
        String authKey = AuthKeyUtil.getPlatformKey(systemId, tenantId);
        UpmAccountPO insertAccountPO = this.insertWxAccount(telephone, systemId);
        UpmUserPO insertUserPO = this.insertWxUser(insertAccountPO.getId(), tenantId, systemId, authKey, telephone, nick);
        // 添加司机角色
        this.userRelDriverRole(insertUserPO.getId());

        UserWxInfoDTO userWxInfoDTO = new UserWxInfoDTO();
        userWxInfoDTO.setId(insertAccountPO.getId());
        userWxInfoDTO.setStatus(StatusEnum.ENABLE);
        userWxInfoDTO.setLoginType(LoginTypeEnum.PHONE);
        userWxInfoDTO.setTelephone(insertAccountPO.getTelephone());
        userWxInfoDTO.setUserId(insertUserPO.getId());
        return userWxInfoDTO;
    }

    /**
     * 创建移动端用户
     *
     * @param systemId
     * @param telephone
     * @return
     */
    public UserWxInfoDTO createAppUser(Long systemId, String telephone, String appId, String cid) {
        Long tenantId = ssoAuthProperties.getPlatformTenantId();
        String authKey = AuthKeyUtil.getPlatformKey(systemId, ssoAuthProperties.getPlatformTenantId());
        // 手机号查账号
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(telephone, systemId);
        if (ObjUtil.isNull(upmAccountPO)) {
            // 不存在,新建
            UpmAccountPO insertAccountPO = this.insertWxAccount(telephone, systemId);
            UpmUserPO insertUserPO = this.insertWxUser(insertAccountPO.getId(), tenantId, systemId, authKey, telephone, null);
            UpmUserThirdPO upmUserThirdPO = this.insertAppUserThird(insertUserPO.getId(), appId, cid);
            return this.arrangeWxInfo(insertAccountPO, insertUserPO, upmUserThirdPO);
        }
        // 司机userId查用户
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndAuthKey(upmAccountPO.getId(), authKey);
        if (ObjUtil.isNull(upmUserPO)) {
            // 不存在,新建
            UpmUserPO insertUserPO = this.insertWxUser(upmAccountPO.getId(), tenantId, systemId, authKey, telephone, null);
            UpmUserThirdPO upmUserThirdPO = this.insertAppUserThird(insertUserPO.getId(), appId, cid);
            return this.arrangeWxInfo(upmAccountPO, insertUserPO, upmUserThirdPO);
        }
        // 新建三方关联
        UpmUserThirdPO upmUserThirdPO = this.insertAppUserThird(upmUserPO.getId(), appId, cid);
        return this.arrangeWxInfo(upmAccountPO, upmUserPO, upmUserThirdPO);
    }

    /**
     * 账号登录关联三方表
     */
    public UserWxInfoDTO accountCreateAppUser(Long systemId, String appId, String username, String cid) {
        String authKey = AuthKeyUtil.getPlatformKey(systemId, ssoAuthProperties.getPlatformTenantId());
        // 查账号
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(username, systemId);
        if (ObjUtil.isNull(upmAccountPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 司机userId查用户
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndAuthKey(upmAccountPO.getId(), authKey);
        if (ObjUtil.isNull(upmUserPO)) {
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        // 新建三方关联
        UpmUserThirdPO upmUserThirdPO = this.insertAppUserThird(upmUserPO.getId(), appId, cid);
        return this.arrangeWxInfo(upmAccountPO, upmUserPO, upmUserThirdPO);
    }

    /**
     * 司机认证
     *
     * @return
     */
    public Boolean certificationDriver(Long userId) {
        this.userRelDriverRole(userId);
        return true;
    }

    /**
     * 创建微信公众号用户
     *
     * @param officialAccountUser
     */
    public OfficialAccountUser createOfficialUser(OfficialAccountUser officialAccountUser) {
        officialAccountUser.fillAuthKey();
        officialAccountUser.setPassword(upmSeverProperties.getOfficialPassword());
        //1. 创建账号
        Long accountId = createOfficialAccount(officialAccountUser);
        officialAccountUser.setAccountId(accountId);
        //2. 创建用户
        Long userId = createOfficialInnerUser(officialAccountUser);
        officialAccountUser.setUserId(userId);
        //3. 创建三方微信用户
        createOfficialUserThird(officialAccountUser);
        return officialAccountUser;
    }

    private Long createOfficialUserThird(OfficialAccountUser officialAccountUser) {
        UserWxInfoDTO userWxInfoDTO = userThirdMapper.selectByPhoneAndOpenId(officialAccountUser.getTelephone(), officialAccountUser.getOpenId(), officialAccountUser.getSystemId(), officialAccountUser.getAppId());
        if (ObjUtil.isNotNull(userWxInfoDTO)) {
            return userWxInfoDTO.getUserId();
        }
        UpmUserThirdPO upmUserThirdPO = insertOfficialUserThird(officialAccountUser.getUserId(), officialAccountUser.getAppId(), officialAccountUser.getOpenId());
        return upmUserThirdPO.getId();
    }

    private Long createOfficialInnerUser(OfficialAccountUser officialAccountUser) {
        UpmUserPO upmUserPO = upmUserMapper.selectByAccountIdAndAuthKey(officialAccountUser.getAccountId(), officialAccountUser.getAuthKey());
        if (Objects.nonNull(upmUserPO)) {
            return upmUserPO.getId();
        }
        UpmUser user = UpmUser.fromOfficialAccountUser(officialAccountUser);
        return userRepository.createUser(user);
    }


    private Long createOfficialAccount(OfficialAccountUser officialAccountUser) {
        UpmAccount account = UpmAccount.fromOfficialAccountUser(officialAccountUser);
        UpmAccountPO upmAccountPO = accountMapper.selectByTelephoneAndSystemId(account.getTelephone(), account.getSystemId());
        if (Objects.nonNull(upmAccountPO)) {
            return upmAccountPO.getId();
        }
        return accountRepository.createAccount(account);
    }


    private void userRelDriverRole(Long userId) {
        String driveDefaultRole = upmSeverProperties.getDriveDefaultRole();
        UpmRolePO rolePO = roleMapper.queryByCode(driveDefaultRole);
        if (ObjUtil.isNull(rolePO)) {
            log.error("数据库中请配置小程序司机端的固定角色");
            throw new BusinessException(MessageCode.DEFAULT_BIZ_CODE);
        }
        UpmUserRolePO relPO = userRoleMapper.selectByUserIdAndRoleId(userId, rolePO.getId());
        if (ObjUtil.isNull(relPO)) {
            UpmUserRolePO userRolePO = new UpmUserRolePO();
            userRolePO.setUserId(userId);
            userRolePO.setRoleId(rolePO.getId());
            userRoleMapper.insert(userRolePO);
        }
    }

    private UpmAccountPO insertWxAccount(String phoneNumber, Long systemId) {
        UpmAccountPO accountPO = new UpmAccountPO();
        accountPO.setTelephone(phoneNumber);
        accountPO.setUsername(phoneNumber);
        Password password = Password.create(upmSeverProperties.getMiniPassword());
        accountPO.setPassword(password.getPassword());
        accountPO.setSalt(password.getSalt());
        accountPO.setLoginType(LoginTypeEnum.PHONE);
        accountPO.setType(AccountTypeEnum.DEFAULT);
        accountPO.setSystemId(systemId);
        accountMapper.insert(accountPO);
        return accountPO;
    }

    private UpmUserPO insertWxUser(Long accountId, Long tenantId, Long systemId, String authKey, String phoneNumber, String nick) {
        UpmUserPO userPO = new UpmUserPO();
        userPO.setAccountId(accountId);
        userPO.setTenantId(tenantId);
        userPO.setSystemId(systemId);
        userPO.setAuthKey(authKey);
        userPO.setTelephone(phoneNumber);
        userPO.setNick(StrUtil.isNotBlank(nick) ? nick : phoneNumber);
        userPO.setType(UserTypeEnum.TENANT);
        upmUserMapper.insert(userPO);
        return userPO;
    }

    public UpmUserThirdPO insertUserThird(Long userId, String appId, WxMaJscode2SessionResult wxMaUserInfo) {
        UpmUserThirdPO userThirdPO = new UpmUserThirdPO();
        userThirdPO.setType(ThridTypeEnum.WX);
        userThirdPO.setUserId(userId);
        userThirdPO.setUnionId(wxMaUserInfo.getUnionid());
        userThirdPO.setOpenId(wxMaUserInfo.getOpenid());
        userThirdPO.setAppId(appId);
        userThirdMapper.insert(userThirdPO);
        return userThirdPO;
    }

    private UpmUserThirdPO insertAppUserThird(Long userId, String appId, String cid) {
        UpmUserThirdPO userThirdPO = new UpmUserThirdPO();
        userThirdPO.setType(ThridTypeEnum.APP);
        userThirdPO.setUserId(userId);
        userThirdPO.setOpenId(cid);
        userThirdPO.setAppId(appId);
        userThirdMapper.insert(userThirdPO);
        return userThirdPO;
    }


    private UpmUserThirdPO insertOfficialUserThird(Long userId, String appId, String openId) {
        UpmUserThirdPO userThirdPO = new UpmUserThirdPO();
        userThirdPO.setType(ThridTypeEnum.OFFICIAL_ACCOUNT);
        userThirdPO.setUserId(userId);
        userThirdPO.setOpenId(openId);
        userThirdPO.setAppId(appId);
        userThirdMapper.insert(userThirdPO);
        return userThirdPO;
    }

    private UserWxInfoDTO arrangeWxInfo(UpmAccountPO accountPO, UpmUserPO upmUserPO, UpmUserThirdPO upmUserThirdPO) {
        UserWxInfoDTO userWxInfoDTO = new UserWxInfoDTO();
        userWxInfoDTO.setId(accountPO.getId());
        userWxInfoDTO.setStatus(StatusEnum.ENABLE);
        userWxInfoDTO.setLoginType(LoginTypeEnum.PHONE);
        userWxInfoDTO.setTelephone(accountPO.getTelephone());
        userWxInfoDTO.setUserId(upmUserPO.getId());
        userWxInfoDTO.setUnionId(Optional.ofNullable(upmUserThirdPO).map(UpmUserThirdPO::getUnionId).orElse(null));
        userWxInfoDTO.setOpenId(Optional.ofNullable(upmUserThirdPO).map(UpmUserThirdPO::getOpenId).orElse(null));
        userWxInfoDTO.setAppId(Optional.ofNullable(upmUserThirdPO).map(UpmUserThirdPO::getAppId).orElse(null));
        userWxInfoDTO.setAuthKey(upmUserPO.getAuthKey());
        userWxInfoDTO.setUsername(accountPO.getTelephone());
        userWxInfoDTO.setTenantId(upmUserPO.getTenantId());
        userWxInfoDTO.setPassword(accountPO.getPassword());
        userWxInfoDTO.setSalt(accountPO.getSalt());
        return userWxInfoDTO;
    }

}
