package cn.genn.trans.upm.application.service.action;

import cn.binarywang.wx.miniapp.bean.WxMaJscode2SessionResult;
import cn.dev33.satoken.stp.StpUtil;
import cn.genn.core.exception.BusinessException;
import cn.genn.trans.base.config.UpmSeverProperties;
import cn.genn.trans.upm.application.assembler.UpmUserAssembler;
import cn.genn.trans.upm.application.assembler.UpmUserReturnAssembler;
import cn.genn.trans.upm.application.dto.UserAccountDTO;
import cn.genn.trans.upm.application.processor.UserProcessor;
import cn.genn.trans.upm.application.service.query.UpmUserQueryService;
import cn.genn.trans.upm.application.service.query.WxMiniQueryService;
import cn.genn.trans.upm.domain.upm.model.entity.UpmAccount;
import cn.genn.trans.upm.domain.upm.model.entity.UpmSystem;
import cn.genn.trans.upm.domain.upm.model.entity.UpmUser;
import cn.genn.trans.upm.domain.upm.repository.UserRepository;
import cn.genn.trans.upm.domain.upm.service.SystemDomainService;
import cn.genn.trans.upm.domain.upm.service.UserDomainService;
import cn.genn.trans.upm.infrastructure.config.mini.WxMaProperties;
import cn.genn.trans.upm.infrastructure.exception.MessageCode;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserMapper;
import cn.genn.trans.upm.infrastructure.repository.mapper.UpmUserThirdMapper;
import cn.genn.trans.upm.infrastructure.repository.po.UpmAccountPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserPO;
import cn.genn.trans.upm.infrastructure.repository.po.UpmUserThirdPO;
import cn.genn.trans.upm.infrastructure.utils.SsoTokenUtil;
import cn.genn.trans.upm.interfaces.base.web.context.CurrentUserHolder;
import cn.genn.trans.upm.interfaces.base.web.dto.SsoUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.base.web.properties.SsoAuthProperties;
import cn.genn.trans.upm.interfaces.command.*;
import cn.genn.trans.upm.interfaces.command.mini.MiniSsoThirdDataSaveCommand;
import cn.genn.trans.upm.interfaces.dto.LoginUserAuthInfoDTO;
import cn.genn.trans.upm.interfaces.dto.UpmUserDTO;
import cn.genn.trans.upm.interfaces.dto.mini.UserWxInfoDTO;
import cn.genn.trans.upm.interfaces.enums.*;
import cn.genn.trans.upm.interfaces.utils.AuthKeyUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.StringUtils;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * 应用操作服务,负责增删改的实现.事务在这一层控制
 *
 * <AUTHOR>
 */
@Service
@Slf4j
public class UpmUserActionService {

    @Resource
    private UserDomainService userDomainService;
    @Resource
    private UpmUserAssembler userAssembler;
    @Resource
    private UserRepository repository;
    @Resource
    private UpmUserQueryService queryService;
    @Resource
    private SsoTokenActionService ssoTokenActionService;
    @Resource
    private UserProcessor userProcessor;
    @Resource
    private SsoAccountMiniActionService ssoAccountMiniActionService;
    @Resource
    private UpmUserMapper upmUserMapper;
    @Resource
    private SsoAuthProperties ssoAuthProperties;

    @Resource
    private SystemDomainService systemDomainService;

    @Resource
    private UpmUserReturnAssembler upmUserReturnAssembler;
    @Autowired
    private UpmSeverProperties upmSeverProperties;
    @Resource
    private WxMiniQueryService wxMiniQueryService;

    @Resource
    private UpmUserThirdMapper userThirdMapper;

    @Resource
    private WxMaProperties wxMaProperties;

    /**
     * 新增
     *
     * @return Long
     */
    @Transactional(rollbackFor = Exception.class)
    public UpmUserDTO save(UpmUserSaveCommand command) {
        userProcessor.checkSave(command);
        String authKey = CurrentUserHolder.getAuthKey();
        UpmUser upmUser = userAssembler.UpmUserSaveCommand2UpmUser(command);
        upmUser.setTenantId(CurrentUserHolder.getTenantId());
        upmUser.setSystemId(CurrentUserHolder.getSystemId());
        upmUser.setAuthKey(authKey);
        return userAssembler.PO2DTO(userDomainService.createTenantUser(upmUser));
    }

    @Transactional(rollbackFor = Exception.class)
    public List<UpmUserDTO> saveBySystemType(UpmUserSaveBySystemTypeCommand command) {
        userProcessor.checkBeforeSaveBySystemType(command);
        List<UpmSystem> systems = systemDomainService.findBySystemType(command.getSystemType());
        if (CollectionUtil.isEmpty(systems)) {
            throw new BusinessException(MessageCode.SYSTEM_REOURCE_NOT_EXIST_CANT_CREATE_USER);
        }
        Long tenantId = CurrentUserHolder.getTenantId();
        // 创建不同系统的用户
        List<UpmUser> userList = systems.stream().map(system -> {
            String authKey = AuthKeyUtil.getAuthKey(command.getAuthGroup(), system.getId(), tenantId, command.getOriginId());
            UpmUser upmUser = userAssembler.upmUserSaveBySystemType2UpmUser(command);
            upmUser.setTenantId(tenantId);
            upmUser.setSystemId(system.getId());
            upmUser.setAuthKey(authKey);
            upmUser.setRoleIdList(command.getMainRoleIds());
            return upmUser;
        }).collect(Collectors.toList());
        List<UpmUser> upmUsers = userDomainService.batchCreateTenantUserBySystem(userList);
        return upmUserReturnAssembler.entity2DTO(upmUsers);
    }

    /**
     * 用户注册,初始化游客默认角色;
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean userRegister(UserRegisTerCommand command) {
        userProcessor.checkUserRegister(command);
        return userDomainService.createRegisterUser(userAssembler.UserRegisTerCommand2UpmUser(command));
    }

    /**
     * 修改
     *
     * @return Boolean
     */
    public Boolean update(UpmUserUpdateCommand command) {
        UpmUserPO upmUserPO = userProcessor.checkUpdate(command);
        UpmUser upmUser = new UpmUser()
            .setId(command.getId())
            .setTelephone(command.getTelephone())
            .setStatus(command.getStatus())
            .setNick(command.getNick())
            .setEmail(command.getEmail())
            .setAccountId(upmUserPO.getAccountId())
            .setCompanyName(command.getCompanyName())
            .setSaleName(command.getSaleName())
            .setEffectiveTime(command.getEffectiveTime())
            .setSaleName(command.getSaleName())
            .setRoleIdList(command.getRoleIdList());
        userDomainService.update(upmUser, Optional.ofNullable(command.getClearSign()).orElse(true));
        //todo:有效期处理
//        handlerEffectiveTime(command.getId(), command.getEffectiveTime());
        return true;
    }

    private void handlerEffectiveTime(Long userId, LocalDateTime effectiveTime){
        UpmUserDTO upmUserDTO = upmUserMapper.selectByUserId(userId);
        if(ObjUtil.isNull(upmUserDTO)){
            throw new BusinessException(MessageCode.USER_NOT_EXIST);
        }
        if(ObjUtil.isNotNull(effectiveTime) && effectiveTime.equals(upmUserDTO.getEffectiveTime())){
            return;
        }

        //刷新用户上下文
        List<LoginUserAuthInfoDTO> userAuthInfos = ssoAccountMiniActionService.arrangeTokensByUseId(upmUserDTO.getAccountId(), Collections.singletonList(userId));
        if(CollUtil.isNotEmpty(userAuthInfos)){
            LoginUserAuthInfoDTO loginUserAuthInfoDTO = userAuthInfos.get(0);
            if(effectiveTime.isBefore(LocalDateTime.now())){
                //直接踢出用户
                StpUtil.logout(SsoTokenUtil.generateUserLoginId(upmUserDTO.getAccountId(), upmUserDTO.getId()));
                return;
            }
            //更新用户上下文
            loginUserAuthInfoDTO.setEffectiveTime(effectiveTime);
            SsoTokenUtil.setLoginUserAuthInfo(loginUserAuthInfoDTO.getToken(), loginUserAuthInfoDTO);
        }
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean updateBySystemType(UpmUserUpdateBySystemTypeCommand command) {
        // 依照主id 获取到其他系统的同名用户id
        List<UpmUser> upmUsers = getAllSystemUpmUserListBySystemType(command.getSystemType(), command.getId());
        List<UpmUser> newUpmUsers = upmUsers.stream().map(upmUser ->
            new UpmUser()
                .setId(upmUser.getId())
                .setTelephone(command.getTelephone())
                .setStatus(command.getStatus())
                .setNick(command.getNick())
                .setAccountId(upmUser.getAccountId())
                .setRoleIdList(command.getRoleIdList())
        ).collect(Collectors.toList());
        newUpmUsers.forEach(upmUser -> {
            userDomainService.updateBySystemType(upmUser, Optional.ofNullable(command.getClearSign()).orElse(true));
        });
        return true;
    }


    @Transactional(rollbackFor = Exception.class)
    public Boolean changeStatusBySystemType(UpmUserChangeStatusBySystemTypeCommand command) {
        List<UpmUser> upmUsers = getAllSystemUpmUserListBySystemType(command.getSystemType(), command.getId());
        List<Long> userIds = upmUsers.stream().map(UpmUser::getId).collect(Collectors.toList());
        repository.updateStatus(userIds, command.getStatus());
        // 如果是更新停用用户状态 需要注销掉token
        if (command.getStatus().equals(StatusEnum.DISABLE)) {
            ssoTokenActionService.logoutByUserIdList(userIds);
        }
        return true;
    }

    private List<UpmUser> getAllSystemUpmUserListBySystemType(SystemTypeEnum systemType, Long mainUserId) {
        List<UpmSystem> systems = systemDomainService.findBySystemType(systemType);
        List<Long> systemIds = Optional.ofNullable(systems).orElse(new ArrayList<>(0))
            .stream().map(UpmSystem::getId).collect(Collectors.toList());
        List<UpmUser> upmUsers = userDomainService.selectBySystemsAndMainUserId(systemIds, mainUserId);
        return upmUsers;
    }

    /**
     * 批量启用禁用用户
     *
     * @param command
     * @return
     */
    public Boolean changeStatus(UpmChangeStatusCommand command) {
        repository.updateStatus(command.getIdList(), command.getStatus());
        // 如果是更新停用用户状态 需要注销掉token
        if (command.getStatus().equals(StatusEnum.DISABLE)) {
            ssoTokenActionService.logoutByUserIdList(command.getIdList());
        }
        return true;
    }

    /**
     * 重置密码
     *
     * @param command
     * @return
     */
    public Boolean resetPassword(UpmUserResetPasswordCommand command) {
        UpmUserPO upmUserPO = userProcessor.checkResetPassword(command);

        UpmAccount upmAccount = new UpmAccount()
            .setPassword(command.getNewPassword());
        UpmUser upmUser = new UpmUser()
            .setAccount(upmAccount)
            .setId(upmUserPO.getId())
            .setAccountId(upmUserPO.getAccountId());
        return userDomainService.resetPassword(upmUser);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean resetPasswordBySystemType(UpmUserResetPasswordBySystemTypeCommand command) {
        List<UpmSystem> systems = systemDomainService.findBySystemType(command.getSystemType());

        List<Long> systemIds = Optional.ofNullable(systems).orElse(new ArrayList<>(0))
            .stream().map(UpmSystem::getId).collect(Collectors.toList());

        systemIds.forEach(systemId -> {
            userProcessor.checkPasswordBySystemId(systemId, command.getNewPassword());
        });
        List<UpmUser> upmUsers = userDomainService.selectBySystemsAndMainUserId(systemIds, command.getId());

        upmUsers.forEach(upmUser -> {
            UpmAccount upmAccount = new UpmAccount()
                .setPassword(command.getNewPassword());
            UpmUser user = new UpmUser()
                .setAccount(upmAccount)
                .setId(upmUser.getId())
                .setAccountId(upmUser.getAccountId());
            userDomainService.resetPassword(user);
        });
        return true;
    }



    /**
     * 关联角色
     */
    public Boolean relatedRole(UpmUserRoleRelationCommand command) {
        UpmUser upmUser = new UpmUser().setId(command.getUserId()).setRoleIdList(command.getRoleIdList());
        return userDomainService.relatedRole(upmUser);
    }

    /**
     * 更新用户信息
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeUserInfo(UserInfoChangeCommand command) {
        userProcessor.checkChangeUserInfo(command);
        UpmUser upmUser = new UpmUser()
            .setId(CurrentUserHolder.getUserId())
            .setAccountId(CurrentUserHolder.getAccountId())
            .setNick(command.getNick())
            .setRemark(command.getRemark())
            .setEmail(command.getEmail())
            .setAvatar(command.getAvatar());
        userDomainService.changeUserInfo(upmUser);
        //刷新缓存
        //todo:后续有新的小程序类型也要修改这段代码
        if(CurrentUserHolder.getSystemType().equals(SystemTypeEnum.DRIVER)){
            ssoAccountMiniActionService.resetMiniAccountCache(CurrentUserHolder.getAccountId());
        } else {
            ssoTokenActionService.resetAccountCache(CurrentUserHolder.getAccountId());
        }
        return true;
    }

    /**
     * 用户绑定手机
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean bindPhone(UserBindPhoneCommand command) {
        userProcessor.checkBindPhone(command);
        UpmUser upmUser = new UpmUser()
            .setId(CurrentUserHolder.getUserId())
            .setAccountId(CurrentUserHolder.getAccountId())
            .setTelephone(command.getTelephone());
        userDomainService.bindPhone(upmUser);
        //刷新缓存
        return ssoTokenActionService.resetAccountCache(CurrentUserHolder.getAccountId());
    }

    /**
     * 用户更换绑定手机
     *
     * @param command
     * @return
     */
    @Transactional(rollbackFor = Exception.class)
    public Boolean changeBindPhone(UserChangeBindPhoneCommand command) {
        userProcessor.checkChangeBindPhone(command);
        UpmUser upmUser = new UpmUser()
            .setId(CurrentUserHolder.getUserId())
            .setAccountId(CurrentUserHolder.getAccountId())
            .setTelephone(command.getTelephone());
        userDomainService.bindPhone(upmUser);
        //退出登录
        ssoTokenActionService.ssoAccountLogout(upmUser.getAccountId());
        return true;

    }

    /**
     * 密保手机修改密码
     *
     * @param command
     * @return
     */
    public Boolean changePdByPhone(PdChangeByPhoneCommand command) {
        UpmAccountPO accountPO = userProcessor.checkChangePdByPhone(command);
        UpmAccount upmAccount = new UpmAccount()
            .setId(accountPO.getId())
            .setPassword(command.getPassword());
        userDomainService.changePassword(upmAccount);
        Long accountId = Optional.ofNullable(CurrentUserHolder.getCurrentUser()).map(SsoUserAuthInfoDTO::getAccountId).orElse(null);
        if (ObjUtil.isNull(accountId)) {
            //退出登录
            ssoTokenActionService.ssoAccountLogout(upmAccount.getId());
        }
        return true;
    }

    /**
     * 原密码方式修改密码
     *
     * @param command
     * @return
     */
    public Boolean changePdByPd(PdChangeByPdCommand command) {
        userProcessor.checkChangePdByPd(command);
        UpmAccount upmAccount = new UpmAccount()
            .setId(CurrentUserHolder.getAccountId())
            .setPassword(command.getPassword());
        userDomainService.changePassword(upmAccount);
        //退出登录
        ssoTokenActionService.ssoAccountLogout(upmAccount.getId());
        return true;
    }

    /**
     * 批量删除
     */
    public Boolean batchRemove(List<Long> idList) {
        // 系统账号禁止删除
        List<UserAccountDTO> userAccountPOS = queryService.selectUserList(idList);
        boolean systemAccount = userAccountPOS.stream().anyMatch(accountDTO -> accountDTO.getType().contains(AccountTypeEnum.SYSTEM.getCode()));
        if (systemAccount) {
            throw new BusinessException(MessageCode.ACCOUNT_DELETE_ERROR);
        }
        List<UpmUser> deleteUsers = userAccountPOS.stream()
            .map(accountDTO -> new UpmUser().setId(accountDTO.getId()).setAccountId(accountDTO.getAccountId()))
            .collect(Collectors.toList());
        return userDomainService.removeUsers(deleteUsers);
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean deleteBySystemType(UpmUserDelBySystemTypeCommand command) {
        // 系统账号禁止删除
        List<UpmSystem> systems = systemDomainService.findBySystemType(command.getSystemType());
        if (CollectionUtil.isEmpty(systems)) {
            throw new BusinessException(MessageCode.SYSTEM_REOURCE_NOT_EXIST_CANT_DEL_USER);
        }
        List<UpmUser> upmUsers = userDomainService.selectBySystemsAndMainUserId(
            systems.stream().map(UpmSystem::getId).collect(Collectors.toList()), command.getId());

        boolean existSystemAccount = upmUsers.stream().anyMatch(user -> UserTypeEnum.SYSTEM.equals(user.getType()));
        if (existSystemAccount) {
            throw new BusinessException(MessageCode.ACCOUNT_DELETE_ERROR);
        }
        return userDomainService.removeUsers(upmUsers);
    }

    /**
     * 司机upm注册
     *
     * @param command
     * @return
     */
    public UserWxInfoDTO driverRegister(DriverRegisterCommand command) {
        if(StrUtil.isBlank(command.getSystemCode())){
            command.setSystemCode(upmSeverProperties.getMiniDriveCode());
        }
        UpmSystem upmSystem = userProcessor.checkDriverRegister(command);

        UserWxInfoDTO registerDriver = userDomainService.createRegisterDriver(command.getTelephone(), command.getNick(),upmSystem.getId());
        //刷新用户缓存
        ssoAccountMiniActionService.resetMiniAccountCache(registerDriver.getId());
        return registerDriver;
    }

    /**
     * 司机认证
     */
    public Boolean certificationDriver(DriverCertificationCommand command) {
        userDomainService.certificationDriver(command.getUserId());
        //刷新用户缓存
        UpmUserPO upmUserPO = upmUserMapper.selectById(command.getUserId());
        ssoAccountMiniActionService.resetMiniAccountCache(upmUserPO.getAccountId());
        return true;
    }

    /**
     * 司机upm注册
     *
     * @param command
     * @return
     */
    public void saveThirdData(MiniSsoThirdDataSaveCommand command) {
        String appId = wxMaProperties.getConfigs().get(0).getAppid();
        WxMaJscode2SessionResult wxMaUserInfo = wxMiniQueryService.miniUserInfo(appId, command.getLoginCode());
        List<UpmUserThirdPO> upmUserThirdPOS = userThirdMapper.selectList(Wrappers.lambdaQuery(UpmUserThirdPO.class)
            .eq(UpmUserThirdPO::getAppId, appId)
            .eq(UpmUserThirdPO::getOpenId, wxMaUserInfo.getOpenid())
            .eq(UpmUserThirdPO::getUserId, command.getUserId())
            .eq(UpmUserThirdPO::getType, ThridTypeEnum.WX)
            .eq(UpmUserThirdPO::getStatus, StatusEnum.ENABLE));
        if (CollectionUtil.isEmpty(upmUserThirdPOS)) {
            userThirdMapper.closeStatusByUserIdAndAppId(command.getUserId(), appId);
            userDomainService.insertUserThird(command.getUserId(), appId, wxMaUserInfo);
        }
    }

    public Boolean createThirdUserInfo(UpmUserThirdOperateCommand command) {
        log.info("createThirdUserInfo command:{}", command);
        userThirdMapper.createFSThirdInfo(command.getUserId(), command.getAppId(), command.getOpenId(),null);
        return Boolean.TRUE;
    }

    @Transactional(rollbackFor = Exception.class)
    public Boolean updateFsThirdInfoStatus(UpmUserThirdUpdateStatusCommand command) {
        if(command.getCloseUserId()!=null){
            userThirdMapper.closeStatusByUserIdsAndAppId(command.getCloseUserId(), command.getAppId());
        }
        if(command.getNewUserId()!=null&& StringUtils.isNotBlank(command.getNewOpenId())){
            userThirdMapper.createFSThirdInfo(command.getNewUserId(), command.getAppId(), command.getNewOpenId(),null);
        }
        return Boolean.TRUE;
    }
}

